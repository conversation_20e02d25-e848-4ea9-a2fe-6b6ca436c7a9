<?php
/**
 * Database Manager for IPTV XUI One Content Manager
 * ================================================
 * 
 * Handles all database operations with connection pooling,
 * caching, and optimized queries for XUI One database.
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/settings.php';

class DatabaseManager {
    private $pdo;
    private $connectionPool = [];
    private $cache = [];
    private $logger;
    private $stats = [
        'queries_executed' => 0,
        'cache_hits' => 0,
        'cache_misses' => 0,
        'connection_time' => 0
    ];

    public function __construct() {
        $this->logger = $this->initializeLogger();
        $this->initializeConnection();
    }

    /**
     * Initialize database connection with retry logic
     */
    private function initializeConnection() {
        $config = DB_CONFIG;
        $attempts = 0;
        $maxAttempts = CONNECTION_POOL['retry_attempts'];

        while ($attempts < $maxAttempts) {
            try {
                $startTime = microtime(true);
                
                $dsn = sprintf(
                    "mysql:host=%s;port=%d;dbname=%s;charset=%s",
                    $config['host'],
                    $config['port'],
                    $config['database'],
                    $config['charset']
                );

                $this->pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);

                // Configure connection to prevent long threads
                $this->pdo->exec("SET SESSION wait_timeout = 30");
                $this->pdo->exec("SET SESSION interactive_timeout = 30");
                $this->pdo->exec("SET SESSION net_read_timeout = 10");
                $this->pdo->exec("SET SESSION net_write_timeout = 10");
                $this->pdo->setAttribute(PDO::ATTR_TIMEOUT, 10);

                $this->stats['connection_time'] = microtime(true) - $startTime;
                $this->log('info', 'Database connection established successfully');
                
                // Validate XUI One tables
                $this->validateXUITables();
                return;

            } catch (PDOException $e) {
                $attempts++;
                $this->log('error', "Database connection attempt {$attempts} failed: " . $e->getMessage());
                
                if ($attempts < $maxAttempts) {
                    sleep(CONNECTION_POOL['retry_delay']);
                } else {
                    throw new Exception("Failed to connect to database after {$maxAttempts} attempts: " . $e->getMessage());
                }
            }
        }
    }

    /**
     * Validate that required XUI One tables exist
     */
    private function validateXUITables() {
        $requiredTables = ['streams', 'streams_categories', 'streams_series', 'streams_episodes'];
        $missingTables = [];

        foreach ($requiredTables as $table) {
            // Use direct query with proper escaping
            $escapedTable = $this->pdo->quote($table);
            $sql = "SHOW TABLES LIKE $escapedTable";
            $stmt = $this->pdo->query($sql);

            if (!$stmt->fetch()) {
                $missingTables[] = $table;
            }
        }

        if (!empty($missingTables)) {
            throw new Exception("Missing required XUI One tables: " . implode(', ', $missingTables));
        }

        $this->log('info', 'XUI One database structure validated successfully');
    }

    /**
     * Execute a query with caching support
     */
    public function query($sql, $params = [], $cacheKey = null, $cacheTtl = null) {
        $this->stats['queries_executed']++;

        // Check cache first
        if ($cacheKey && $this->isCacheEnabled()) {
            $cached = $this->getFromCache($cacheKey);
            if ($cached !== null) {
                $this->stats['cache_hits']++;
                return $cached;
            }
            $this->stats['cache_misses']++;
        }

        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            $result = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Cache the result
            if ($cacheKey && $this->isCacheEnabled()) {
                $this->setCache($cacheKey, $result, $cacheTtl);
            }

            $this->log('debug', "Query executed: {$sql}", ['params' => $params, 'rows' => count($result)]);
            return $result;

        } catch (PDOException $e) {
            $this->log('error', "Query failed: {$sql}", ['error' => $e->getMessage(), 'params' => $params]);
            throw new Exception("Database query failed: " . $e->getMessage());
        }
    }

    /**
     * Execute an update/insert/delete query
     */
    public function execute($sql, $params = []) {
        $this->stats['queries_executed']++;

        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            $affectedRows = $stmt->rowCount();

            $this->log('debug', "Execute query: {$sql}", ['params' => $params, 'affected_rows' => $affectedRows]);
            return $affectedRows;

        } catch (PDOException $e) {
            $this->log('error', "Execute failed: {$sql}", ['error' => $e->getMessage(), 'params' => $params]);
            throw new Exception("Database execute failed: " . $e->getMessage());
        }
    }

    /**
     * Get content statistics for dashboard
     */
    public function getContentStats() {
        $cacheKey = 'content_stats';
        return $this->query(
            SQL_QUERIES['get_content_stats'],
            [],
            $cacheKey,
            CACHE_CONFIG['stats_ttl']
        )[0] ?? [];
    }

    /**
     * Get quality statistics
     */
    public function getQualityStats() {
        $cacheKey = 'quality_stats';
        return $this->query(
            SQL_QUERIES['get_quality_stats'],
            [],
            $cacheKey,
            CACHE_CONFIG['stats_ttl']
        )[0] ?? [];
    }

    /**
     * Get duplicate content analysis
     */
    public function getDuplicateAnalysis($limit = 50, $offset = 0) {
        return $this->query(
            SQL_QUERIES['get_duplicate_analysis'],
            [$limit, $offset]
        );
    }

    /**
     * Get movies with pagination
     */
    public function getMovies($limit = 50, $offset = 0, $filters = []) {
        // Build the base query
        $sql = "SELECT s.*, sc.category_name
                FROM streams s
                LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
                WHERE s.type = 2";

        $params = [];

        // Apply filters
        if (!empty($filters['search'])) {
            $sql .= " AND s.stream_display_name LIKE ?";
            $params[] = '%' . $filters['search'] . '%';
        }

        if (!empty($filters['category'])) {
            $sql .= " AND FIND_IN_SET(?, s.category_id)";
            $params[] = $filters['category'];
        }

        // Add ordering and pagination
        $sql .= " ORDER BY s.added DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;

        return $this->query($sql, $params);
    }

    /**
     * Get symlink content (protected content)
     */
    public function getSymlinkContent($limit = 50, $offset = 0) {
        return $this->query(
            SQL_QUERIES['get_symlink_content'],
            [$limit, $offset]
        );
    }

    /**
     * Get direct source content (manageable content)
     */
    public function getDirectSourceContent($limit = 50, $offset = 0) {
        return $this->query(
            SQL_QUERIES['get_direct_source_content'],
            [$limit, $offset]
        );
    }

    /**
     * Get 4K content (highest priority)
     */
    public function get4KContent($limit = 50, $offset = 0) {
        return $this->query(
            SQL_QUERIES['get_4k_content'],
            [$limit, $offset]
        );
    }

    /**
     * Get 60fps content (high priority)
     */
    public function get60FpsContent($limit = 50, $offset = 0) {
        return $this->query(
            SQL_QUERIES['get_60fps_content'],
            [$limit, $offset]
        );
    }

    /**
     * Search content
     */
    public function searchContent($searchTerm, $limit = 50, $offset = 0) {
        $searchPattern = '%' . $searchTerm . '%';
        return $this->query(
            SQL_QUERIES['search_content'],
            [$searchPattern, $searchPattern, $limit, $offset]
        );
    }

    /**
     * Get content missing TMDB data
     */
    public function getMissingTMDBContent($limit = 50, $offset = 0) {
        return $this->query(
            SQL_QUERIES['search_missing_tmdb'],
            [$limit, $offset]
        );
    }

    /**
     * Insert new stream from M3U
     */
    public function insertStreamFromM3U($streamData) {
        return $this->execute(
            SQL_QUERIES['insert_stream'],
            [
                $streamData['type'] ?? 2,
                $streamData['category_id'] ?? '[]',
                $streamData['stream_display_name'] ?? '',
                $streamData['stream_source'] ?? '',
                $streamData['stream_icon'] ?? '',
                $streamData['tmdb_id'] ?? null,
                $streamData['year'] ?? null,
                $streamData['rating'] ?? 0,
                $streamData['movie_properties'] ?? '{}',
                $streamData['added'] ?? time(),
                $streamData['updated'] ?? date('Y-m-d H:i:s'),
                $streamData['movie_symlink'] ?? 0,
                $streamData['direct_source'] ?? 1
            ]
        );
    }

    /**
     * Update stream with TMDB data
     */
    public function updateStreamTMDB($streamId, $tmdbData) {
        return $this->execute(
            SQL_QUERIES['update_stream_tmdb'],
            [
                $tmdbData['tmdb_id'] ?? null,
                json_encode($tmdbData['movie_properties'] ?? []),
                $tmdbData['year'] ?? null,
                $tmdbData['rating'] ?? 0,
                date('Y-m-d H:i:s'),
                $streamId
            ]
        );
    }

    /**
     * Delete stream
     */
    public function deleteStream($streamId) {
        return $this->execute(SQL_QUERIES['delete_stream'], [$streamId]);
    }

    /**
     * Get categories
     */
    public function getCategories($type = null) {
        $sql = $type ? SQL_QUERIES['get_movie_categories'] : SQL_QUERIES['get_categories'];
        $cacheKey = 'categories_' . ($type ?? 'all');
        
        return $this->query($sql, [], $cacheKey, CACHE_CONFIG['default_ttl']);
    }

    /**
     * Get popular content
     */
    public function getPopularContent($limit = 10) {
        return $this->query(SQL_QUERIES['get_popular_content'], [$limit]);
    }

    /**
     * Get recent additions
     */
    public function getRecentAdditions($limit = 10) {
        return $this->query(SQL_QUERIES['get_recent_additions'], [$limit]);
    }

    /**
     * Cache management methods
     */
    private function isCacheEnabled() {
        return CACHE_CONFIG['enabled'];
    }

    private function getFromCache($key) {
        if (!isset($this->cache[$key])) {
            return null;
        }

        $item = $this->cache[$key];
        if ($item['expires'] < time()) {
            unset($this->cache[$key]);
            return null;
        }

        return $item['data'];
    }

    private function setCache($key, $data, $ttl = null) {
        $ttl = $ttl ?? CACHE_CONFIG['default_ttl'];
        $this->cache[$key] = [
            'data' => $data,
            'expires' => time() + $ttl
        ];
    }

    /**
     * Get database statistics
     */
    public function getStats() {
        return $this->stats;
    }

    /**
     * Test database connection
     */
    public function testConnection() {
        try {
            $this->query("SELECT 1");
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }

    /**
     * Commit transaction
     */
    public function commit() {
        return $this->pdo->commit();
    }

    /**
     * Rollback transaction
     */
    public function rollback() {
        return $this->pdo->rollback();
    }

    /**
     * Get last insert ID
     */
    public function getLastInsertId() {
        return $this->pdo->lastInsertId();
    }

    /**
     * Initialize logger
     */
    private function initializeLogger() {
        // Simple file-based logger
        return new class {
            public function log($level, $message, $context = []) {
                if (LOGGING_CONFIG['enabled']) {
                    $logEntry = sprintf(
                        "[%s] %s: %s %s\n",
                        date('Y-m-d H:i:s'),
                        strtoupper($level),
                        $message,
                        !empty($context) ? json_encode($context) : ''
                    );
                    file_put_contents(LOGGING_CONFIG['file_path'], $logEntry, FILE_APPEND | LOCK_EX);
                }
            }
        };
    }

    private function log($level, $message, $context = []) {
        $this->logger->log($level, $message, $context);
    }
}
