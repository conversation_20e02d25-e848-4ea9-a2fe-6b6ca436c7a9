<?php
/**
 * Dashboard Page - IPTV XUI One Content Manager
 * ============================================
 *
 * Main dashboard with statistics, charts, and quick actions
 */

// Set reasonable timeouts
set_time_limit(30);
ini_set('max_execution_time', 30);

// Session is already initialized by parent
// Include database manager with fallback to demo mode
$demoMode = false;
$error = null;

try {
    // Try to load database managers
    if (file_exists(__DIR__ . '/../../core/FastDatabaseManager.php')) {
        require_once __DIR__ . '/../../core/FastDatabaseManager.php';
        $db = FastDatabaseManager::getInstance();
    } elseif (file_exists(__DIR__ . '/../../core/DatabaseManager.php')) {
        require_once __DIR__ . '/../../core/DatabaseManager.php';
        $db = new DatabaseManager();
    } else {
        throw new Exception("No database manager found");
    }

    // Test connection
    if (method_exists($db, 'testConnection') && !$db->testConnection()) {
        throw new Exception("Database connection failed");
    }

    // Get statistics from database
    $contentStats = method_exists($db, 'getContentStats') ? $db->getContentStats() : [];
    $qualityStats = method_exists($db, 'getQualityStats') ? $db->getQualityStats() : [];
    $recentContent = method_exists($db, 'getRecentAdditions') ? $db->getRecentAdditions(5) : [];
    $popularContent = method_exists($db, 'getPopularContent') ? $db->getPopularContent(5) : [];

} catch (Exception $e) {
    // Fallback to demo mode
    $demoMode = true;
    $error = $e->getMessage();

    // Load demo data manager
    require_once __DIR__ . '/../../core/DemoDataManager.php';
    $db = DemoDataManager::getInstance();

    // Get demo data
    $contentStats = $db->getContentStats();
    $qualityStats = $db->getQualityStats();
    $recentContent = $db->getRecentAdditions(5);
    $popularContent = $db->getPopularContent(5);
}
?>

<div class="page-wrapper">

<?php if ($demoMode): ?>
<div class="alert alert-warning" style="margin-bottom: 2rem;">
    <i class="fas fa-info-circle"></i>
    <strong>Demo Mode Active</strong> - Database connection failed, showing demo data.
    <br><small>Error: <?= htmlspecialchars($error) ?></small>
    <br><a href="../check-requirements.php" style="color: inherit; text-decoration: underline;">Check System Requirements</a>
</div>
<?php elseif (isset($error)): ?>
<div class="error-message">
    <i class="fas fa-exclamation-triangle"></i>
    <strong>Database Connection Error:</strong> <?= htmlspecialchars($error) ?>
    <br><small>Please check your database configuration in config/database.php</small>
</div>
<?php endif; ?>

<!-- Quick Actions -->
<div class="quick-actions">
    <a href="?page=m3u-manager" class="action-card">
        <div class="action-icon">
            <i class="fas fa-upload"></i>
        </div>
        <div class="action-title">Upload M3U</div>
        <div class="action-description">Import new content from M3U files</div>
    </a>
    
    <a href="?page=content-browser" class="action-card">
        <div class="action-icon">
            <i class="fas fa-search"></i>
        </div>
        <div class="action-title">Browse Content</div>
        <div class="action-description">Explore and manage your content</div>
    </a>
    
    <a href="?page=analytics" class="action-card">
        <div class="action-icon">
            <i class="fas fa-chart-bar"></i>
        </div>
        <div class="action-title">View Analytics</div>
        <div class="action-description">Detailed content statistics</div>
    </a>
    
    <a href="#" class="action-card" onclick="enrichTMDBContent()">
        <div class="action-icon">
            <i class="fas fa-magic"></i>
        </div>
        <div class="action-title">Enrich TMDB</div>
        <div class="action-description">Add missing metadata</div>
    </a>
</div>

<!-- Statistics Cards -->
<div class="dashboard-grid">
    <div class="stat-card">
        <div class="stat-header">
            <div class="stat-title">Total Streams</div>
            <div class="stat-icon" style="background: rgba(59, 130, 246, 0.1); color: var(--primary-color);">
                <i class="fas fa-film"></i>
            </div>
        </div>
        <div class="stat-value"><?= number_format($contentStats['total_streams'] ?? 0) ?></div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>All content types</span>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-header">
            <div class="stat-title">Movies</div>
            <div class="stat-icon" style="background: rgba(16, 185, 129, 0.1); color: var(--success-color);">
                <i class="fas fa-video"></i>
            </div>
        </div>
        <div class="stat-value"><?= number_format($contentStats['movies'] ?? 0) ?></div>
        <div class="stat-change positive">
            <i class="fas fa-link"></i>
            <span><?= number_format($contentStats['symlink_movies'] ?? 0) ?> symlink</span>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-header">
            <div class="stat-title">4K Content</div>
            <div class="stat-icon" style="background: rgba(245, 158, 11, 0.1); color: var(--warning-color);">
                <i class="fas fa-crown"></i>
            </div>
        </div>
        <div class="stat-value"><?= number_format($qualityStats['content_4k'] ?? 0) ?></div>
        <div class="stat-change positive">
            <i class="fas fa-shield-alt"></i>
            <span>Protected content</span>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-header">
            <div class="stat-title">60fps Content</div>
            <div class="stat-icon" style="background: rgba(139, 92, 246, 0.1); color: #8b5cf6;">
                <i class="fas fa-tachometer-alt"></i>
            </div>
        </div>
        <div class="stat-value"><?= number_format($qualityStats['content_60fps'] ?? 0) ?></div>
        <div class="stat-change positive">
            <i class="fas fa-star"></i>
            <span>High priority</span>
        </div>
    </div>
</div>

<!-- Charts -->
<div class="chart-container">
    <div class="chart-header">
        <div class="chart-title">Content Distribution</div>
        <div>
            <button class="btn btn-secondary" onclick="refreshCharts()">
                <i class="fas fa-sync-alt"></i>
                Refresh
            </button>
        </div>
    </div>
    <canvas id="contentChart" class="chart-canvas"></canvas>
</div>

<!-- Content Lists -->
<div class="content-grid">
    <div class="content-list">
        <div class="content-list-header">
            <div class="content-list-title">Recent Additions</div>
            <a href="?page=content-browser" class="btn btn-secondary">View All</a>
        </div>
        <?php if (empty($recentContent)): ?>
            <div style="text-align: center; color: var(--text-secondary); padding: 2rem;">
                <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>No recent content found</p>
            </div>
        <?php else: ?>
            <?php foreach ($recentContent as $item): ?>
            <div class="content-item">
                <div class="content-poster">
                    <i class="fas fa-film"></i>
                </div>
                <div class="content-info">
                    <div class="content-title"><?= htmlspecialchars($item['stream_display_name']) ?></div>
                    <div class="content-meta">
                        <?= $item['category_name'] ?? 'Uncategorized' ?> • 
                        <?= $item['year'] ?? 'Unknown year' ?> •
                        Rating: <?= number_format($item['rating'] ?? 0, 1) ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <div class="content-list">
        <div class="content-list-header">
            <div class="content-list-title">Popular Content</div>
            <a href="?page=analytics" class="btn btn-secondary">View Analytics</a>
        </div>
        <?php if (empty($popularContent)): ?>
            <div style="text-align: center; color: var(--text-secondary); padding: 2rem;">
                <i class="fas fa-star" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>No popular content data</p>
            </div>
        <?php else: ?>
            <?php foreach ($popularContent as $item): ?>
            <div class="content-item">
                <div class="content-poster">
                    <i class="fas fa-star"></i>
                </div>
                <div class="content-info">
                    <div class="content-title"><?= htmlspecialchars($item['stream_display_name']) ?></div>
                    <div class="content-meta">
                        <?= $item['category_name'] ?? 'Uncategorized' ?> • 
                        Rating: <?= number_format($item['rating'] ?? 0, 1) ?>/10 •
                        <?= $item['year'] ?? 'Unknown year' ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>

<script>
// Initialize charts and auto-kill threads
document.addEventListener('DOMContentLoaded', function() {
    initializeContentChart();
    startAutoKillThreads();
});

function initializeContentChart() {
    const ctx = document.getElementById('contentChart').getContext('2d');
    
    const data = {
        labels: ['Movies', 'TV Shows', 'Live TV', 'Symlink', 'Direct Source'],
        datasets: [{
            data: [
                <?= $contentStats['movies'] ?? 0 ?>,
                <?= $contentStats['series'] ?? 0 ?>,
                <?= $contentStats['live_tv'] ?? 0 ?>,
                <?= $contentStats['symlink_movies'] ?? 0 ?>,
                <?= $contentStats['direct_movies'] ?? 0 ?>
            ],
            backgroundColor: [
                '#3b82f6',
                '#10b981',
                '#f59e0b',
                '#8b5cf6',
                '#ef4444'
            ],
            borderWidth: 0
        }]
    };

    new Chart(ctx, {
        type: 'doughnut',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        color: '#94a3b8',
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

function refreshCharts() {
    showNotification('Refreshing charts...', 'info');
    setTimeout(() => {
        location.reload();
    }, 1000);
}

function enrichTMDBContent() {
    if (confirm('This will enrich missing TMDB metadata. Continue?')) {
        showNotification('Starting TMDB enrichment...', 'info');
        // TODO: Implement TMDB enrichment via AJAX
    }
}

// Auto-kill long threads system
function startAutoKillThreads() {
    // Run immediately
    autoKillThreads();

    // Then run every 30 seconds
    setInterval(autoKillThreads, 30000);
}

function autoKillThreads() {
    fetch('../auto-kill-threads.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.killed_threads > 0) {
                    showNotification(`🔪 Killed ${data.killed_threads} long database threads`, 'warning', 5000);
                    console.log('Auto-killed threads:', data.killed_details);
                }

                // Update status indicator if exists
                updateThreadStatus(data.status);
            } else {
                console.error('Auto-kill threads failed:', data.error);
            }
        })
        .catch(error => {
            console.error('Auto-kill threads error:', error);
        });
}

function updateThreadStatus(status) {
    // Create or update status indicator
    let statusEl = document.getElementById('thread-status');
    if (!statusEl) {
        statusEl = document.createElement('div');
        statusEl.id = 'thread-status';
        statusEl.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--dark-surface);
            border: 1px solid var(--dark-border);
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 0.75rem;
            color: var(--text-secondary);
            z-index: 1000;
            opacity: 0.8;
        `;
        document.body.appendChild(statusEl);
    }

    const running = status.threads_running || 0;
    const connected = status.threads_connected || 0;

    statusEl.innerHTML = `
        <i class="fas fa-database"></i>
        DB: ${connected} conn, ${running} running
    `;

    // Color based on load
    if (running > 10) {
        statusEl.style.borderColor = '#ef4444';
        statusEl.style.color = '#ef4444';
    } else if (running > 5) {
        statusEl.style.borderColor = '#f59e0b';
        statusEl.style.color = '#f59e0b';
    } else {
        statusEl.style.borderColor = '#10b981';
        statusEl.style.color = '#10b981';
    }
}
</script>

</div> <!-- End page-wrapper -->
