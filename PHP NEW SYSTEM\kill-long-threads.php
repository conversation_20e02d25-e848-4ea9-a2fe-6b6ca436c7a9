<?php
/**
 * KILL LONG THREADS - Emergency Script
 * ===================================
 * 
 * This script monitors and kills long-running database threads
 */

// Include path management
require_once __DIR__ . '/config/paths.php';
require_once getConfigPath('database.php');

class ThreadKiller {
    private $pdo;
    
    public function __construct() {
        $this->connect();
    }
    
    private function connect() {
        try {
            $config = DB_CONFIG;
            $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_TIMEOUT => 5
            ];
            
            $this->pdo = new PDO($dsn, $config['username'], $config['password'], $options);
            
        } catch (PDOException $e) {
            throw new Exception("Connection failed: " . $e->getMessage());
        }
    }
    
    /**
     * Get all running processes
     */
    public function getRunningProcesses() {
        try {
            $stmt = $this->pdo->query("SHOW PROCESSLIST");
            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Kill long running threads
     */
    public function killLongThreads($maxTime = 10) {
        $killed = [];
        $processes = $this->getRunningProcesses();
        
        foreach ($processes as $process) {
            // Skip system processes
            if (in_array($process['Command'], ['Sleep', 'Binlog Dump'])) {
                continue;
            }
            
            // Kill if running too long
            if ($process['Time'] > $maxTime) {
                try {
                    $this->pdo->exec("KILL {$process['Id']}");
                    $killed[] = [
                        'id' => $process['Id'],
                        'time' => $process['Time'],
                        'info' => $process['Info']
                    ];
                } catch (Exception $e) {
                    // Process might already be gone
                }
            }
        }
        
        return $killed;
    }
    
    /**
     * Get database status
     */
    public function getDatabaseStatus() {
        try {
            $stmt = $this->pdo->query("SHOW STATUS LIKE 'Threads_%'");
            $status = [];
            while ($row = $stmt->fetch()) {
                $status[$row['Variable_name']] = $row['Value'];
            }
            return $status;
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Optimize database settings
     */
    public function optimizeSettings() {
        $optimizations = [];
        
        try {
            // Set aggressive timeouts
            $settings = [
                "SET GLOBAL wait_timeout = 30",
                "SET GLOBAL interactive_timeout = 30",
                "SET GLOBAL net_read_timeout = 10",
                "SET GLOBAL net_write_timeout = 10",
                "SET GLOBAL lock_wait_timeout = 5",
                "SET GLOBAL innodb_lock_wait_timeout = 5"
            ];
            
            foreach ($settings as $setting) {
                try {
                    $this->pdo->exec($setting);
                    $optimizations[] = "✅ " . $setting;
                } catch (Exception $e) {
                    $optimizations[] = "❌ " . $setting . " - " . $e->getMessage();
                }
            }
            
        } catch (Exception $e) {
            $optimizations[] = "❌ Failed to optimize: " . $e->getMessage();
        }
        
        return $optimizations;
    }
}

// Execute if called directly
if (basename($_SERVER['SCRIPT_NAME']) === 'kill-long-threads.php') {
    try {
        $killer = new ThreadKiller();
        
        echo "<h1>🔪 LONG THREAD KILLER</h1>";
        
        // Show current processes
        $processes = $killer->getRunningProcesses();
        echo "<h2>📊 Current Processes (" . count($processes) . ")</h2>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>User</th><th>Host</th><th>DB</th><th>Command</th><th>Time</th><th>State</th><th>Info</th></tr>";
        
        foreach ($processes as $process) {
            $timeColor = $process['Time'] > 10 ? 'red' : ($process['Time'] > 5 ? 'orange' : 'green');
            echo "<tr>";
            echo "<td>{$process['Id']}</td>";
            echo "<td>{$process['User']}</td>";
            echo "<td>{$process['Host']}</td>";
            echo "<td>{$process['db']}</td>";
            echo "<td>{$process['Command']}</td>";
            echo "<td style='color: {$timeColor}; font-weight: bold;'>{$process['Time']}s</td>";
            echo "<td>{$process['State']}</td>";
            echo "<td>" . htmlspecialchars(substr($process['Info'] ?? '', 0, 50)) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Kill long threads
        echo "<h2>🔪 Killing Long Threads</h2>";
        $killed = $killer->killLongThreads(10);
        
        if (empty($killed)) {
            echo "<p style='color: green;'>✅ No long threads found!</p>";
        } else {
            echo "<p style='color: red;'>🔪 Killed " . count($killed) . " long threads:</p>";
            echo "<ul>";
            foreach ($killed as $thread) {
                echo "<li>Thread {$thread['id']} (running {$thread['time']}s): " . htmlspecialchars($thread['info'] ?? 'Unknown query') . "</li>";
            }
            echo "</ul>";
        }
        
        // Show database status
        echo "<h2>📈 Database Status</h2>";
        $status = $killer->getDatabaseStatus();
        echo "<ul>";
        foreach ($status as $key => $value) {
            echo "<li><strong>{$key}:</strong> {$value}</li>";
        }
        echo "</ul>";
        
        // Optimize settings
        echo "<h2>⚙️ Optimizing Settings</h2>";
        $optimizations = $killer->optimizeSettings();
        echo "<ul>";
        foreach ($optimizations as $opt) {
            echo "<li>{$opt}</li>";
        }
        echo "</ul>";
        
        echo "<hr>";
        echo "<p><a href='index.php' style='background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 Back to Dashboard</a></p>";
        echo "<p><a href='?refresh=1' style='background: #ef4444; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔄 Refresh & Kill Again</a></p>";
        
    } catch (Exception $e) {
        echo "<h1>❌ Error</h1>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Long Thread Killer - IPTV Manager</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        table {
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 0.5rem;
            width: 100%;
            margin: 1rem 0;
        }
        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #334155;
        }
        th {
            background: #0f172a;
            font-weight: 600;
        }
        h1, h2 {
            color: #3b82f6;
        }
        ul {
            background: #1e293b;
            padding: 1rem;
            border-radius: 0.5rem;
            border: 1px solid #334155;
        }
        a {
            display: inline-block;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }
    </style>
    <meta http-equiv="refresh" content="30">
</head>
<body>
    <!-- Content is generated by PHP above -->
</body>
</html>
