<?php
/**
 * Fast Database Manager - ANTI LONG THREADS
 * ========================================
 * 
 * Ultra-optimized database manager designed to KILL long threads
 */

class FastDatabaseManager {
    private $pdo;
    private $config;
    private static $instance = null;
    
    public function __construct() {
        $this->config = DB_CONFIG;
        $this->connectFast();
    }
    
    /**
     * Singleton pattern to reuse connections
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * ULTRA FAST connection with AGGRESSIVE timeouts
     */
    private function connectFast() {
        try {
            $dsn = "mysql:host={$this->config['host']};port={$this->config['port']};dbname={$this->config['database']};charset=utf8mb4";
            
            // AGGRESSIVE PDO options - NO MERCY FOR LONG THREADS
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_TIMEOUT => 5,  // 5 seconds MAX
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];
            
            $this->pdo = new PDO($dsn, $this->config['username'], $this->config['password'], $options);
            
            // KILL LONG THREADS IMMEDIATELY
            $this->pdo->exec("SET SESSION wait_timeout = 10");
            $this->pdo->exec("SET SESSION interactive_timeout = 10");
            $this->pdo->exec("SET SESSION net_read_timeout = 5");
            $this->pdo->exec("SET SESSION net_write_timeout = 5");
            $this->pdo->exec("SET SESSION lock_wait_timeout = 2");
            $this->pdo->exec("SET SESSION innodb_lock_wait_timeout = 2");
            
        } catch (PDOException $e) {
            throw new Exception("FAST DB CONNECTION FAILED: " . $e->getMessage());
        }
    }
    
    /**
     * Execute query with TIMEOUT PROTECTION
     */
    private function executeWithTimeout($sql, $params = [], $timeout = 3) {
        $startTime = microtime(true);
        
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            $elapsed = microtime(true) - $startTime;
            if ($elapsed > $timeout) {
                throw new Exception("QUERY TIMEOUT: {$elapsed}s > {$timeout}s");
            }
            
            return $stmt->fetchAll();
            
        } catch (PDOException $e) {
            $elapsed = microtime(true) - $startTime;
            throw new Exception("QUERY FAILED in {$elapsed}s: " . $e->getMessage());
        }
    }
    
    /**
     * Test connection - FAST
     */
    public function testConnection() {
        try {
            $result = $this->executeWithTimeout("SELECT 1 as test", [], 2);
            return isset($result[0]['test']) && $result[0]['test'] == 1;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Get content stats - OPTIMIZED
     */
    public function getContentStats() {
        try {
            $sql = "SELECT 
                        COUNT(*) as total_streams,
                        SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END) as live_tv,
                        SUM(CASE WHEN type = 2 THEN 1 ELSE 0 END) as movies,
                        SUM(CASE WHEN type = 3 THEN 1 ELSE 0 END) as series,
                        SUM(CASE WHEN type = 2 AND movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_movies,
                        SUM(CASE WHEN type = 2 AND direct_source = 1 THEN 1 ELSE 0 END) as direct_movies
                    FROM streams 
                    LIMIT 1";
            
            $result = $this->executeWithTimeout($sql, [], 3);
            return $result[0] ?? $this->getDefaultStats();
            
        } catch (Exception $e) {
            return $this->getDefaultStats();
        }
    }
    
    /**
     * Get quality stats - FAST VERSION
     */
    public function getQualityStats() {
        try {
            $sql = "SELECT 
                        SUM(CASE WHEN stream_display_name LIKE '%4K%' THEN 1 ELSE 0 END) as content_4k,
                        SUM(CASE WHEN stream_display_name LIKE '%60fps%' THEN 1 ELSE 0 END) as content_60fps,
                        SUM(CASE WHEN stream_display_name LIKE '%HDR%' THEN 1 ELSE 0 END) as content_hdr,
                        SUM(CASE WHEN stream_display_name LIKE '%1080p%' THEN 1 ELSE 0 END) as content_fhd,
                        SUM(CASE WHEN stream_display_name LIKE '%720p%' THEN 1 ELSE 0 END) as content_hd
                    FROM streams 
                    WHERE type = 2 
                    LIMIT 1";
            
            $result = $this->executeWithTimeout($sql, [], 3);
            return $result[0] ?? $this->getDefaultQualityStats();
            
        } catch (Exception $e) {
            return $this->getDefaultQualityStats();
        }
    }
    
    /**
     * Get recent content - LIGHTNING FAST
     */
    public function getRecentAdditions($limit = 5) {
        try {
            $sql = "SELECT id, stream_display_name, type, added, movie_symlink
                    FROM streams 
                    ORDER BY id DESC 
                    LIMIT ?";
            
            return $this->executeWithTimeout($sql, [$limit], 2);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Get popular content - SIMPLE VERSION
     */
    public function getPopularContent($limit = 5) {
        try {
            $sql = "SELECT id, stream_display_name, type, added, movie_symlink
                    FROM streams 
                    WHERE type = 2 
                    ORDER BY id DESC 
                    LIMIT ?";
            
            return $this->executeWithTimeout($sql, [$limit], 2);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Default stats when DB fails
     */
    private function getDefaultStats() {
        return [
            'total_streams' => 0,
            'live_tv' => 0,
            'movies' => 0,
            'series' => 0,
            'symlink_movies' => 0,
            'direct_movies' => 0
        ];
    }
    
    /**
     * Default quality stats when DB fails
     */
    private function getDefaultQualityStats() {
        return [
            'content_4k' => 0,
            'content_60fps' => 0,
            'content_hdr' => 0,
            'content_fhd' => 0,
            'content_hd' => 0
        ];
    }
    
    /**
     * Force close connection
     */
    public function forceClose() {
        $this->pdo = null;
    }
    
    /**
     * Destructor - FORCE CLOSE
     */
    public function __destruct() {
        $this->forceClose();
    }
}
