-- ANTI LONG THREAD CONFIGURATION
-- ================================
-- Execute these commands in your MySQL/MariaDB to KILL long threads

-- Set AGGRESSIVE timeouts (GLOBAL settings)
SET GLOBAL wait_timeout = 30;
SET GLOBAL interactive_timeout = 30;
SET GLOBAL net_read_timeout = 10;
SET GLOBAL net_write_timeout = 10;
SET GLOBAL lock_wait_timeout = 5;
SET GLOBAL innodb_lock_wait_timeout = 5;

-- Set connection limits
SET GLOBAL max_connections = 50;
SET GLOBAL max_user_connections = 10;

-- Optimize query cache (if enabled)
SET GLOBAL query_cache_size = 16777216;  -- 16MB
SET GLOBAL query_cache_limit = 1048576;  -- 1MB

-- Set thread limits
SET GLOBAL thread_cache_size = 8;
SET GLOBAL max_connect_errors = 10;

-- Optimize InnoDB settings
SET GLOBAL innodb_flush_log_at_trx_commit = 2;
SET GLOBAL innodb_buffer_pool_size = 134217728;  -- 128MB

-- Show current settings
SHOW VARIABLES LIKE '%timeout%';
SHOW VARIABLES LIKE '%connection%';

-- Kill any existing long-running queries (BE CAREFUL!)
-- SELECT CONCAT('KILL ', id, ';') FROM INFORMATION_SCHEMA.PROCESSLIST WHERE TIME > 10 AND COMMAND != 'Sleep';

-- Monitor processes
SELECT 
    ID,
    USER,
    HOST,
    DB,
    COMMAND,
    TIME,
    STATE,
    LEFT(INFO, 50) as QUERY_START
FROM INFORMATION_SCHEMA.PROCESSLIST 
WHERE TIME > 5 
ORDER BY TIME DESC;

-- Show thread statistics
SHOW STATUS LIKE 'Threads_%';
SHOW STATUS LIKE 'Connection%';
SHOW STATUS LIKE 'Aborted_%';
