<?php
/**
 * Settings Page - IPTV XUI One Content Manager
 * ===========================================
 * 
 * Application configuration and preferences
 */
?>

<style>
.settings-grid {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 2rem;
}

.settings-nav {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 1.5rem;
    height: fit-content;
}

.settings-nav-item {
    display: block;
    padding: 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.settings-nav-item:hover {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.settings-nav-item.active {
    background: var(--primary-color);
    color: white;
}

.settings-content {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
}

.settings-section {
    display: none;
}

.settings-section.active {
    display: block;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--dark-border);
}

.form-group {
    margin-bottom: 2rem;
}

.form-label {
    display: block;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    transition: var(--transition);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--dark-border);
    transition: var(--transition);
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: var(--transition);
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.settings-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--dark-border);
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-indicator.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-indicator.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.status-indicator.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.config-card {
    background: var(--dark-bg);
    border: 1px solid var(--dark-border);
    border-radius: 0.75rem;
    padding: 1.5rem;
}

.config-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.config-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
}

.config-title {
    font-weight: 600;
    color: var(--text-primary);
}

.config-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}
</style>

<div class="settings-grid">
    <!-- Settings Navigation -->
    <div class="settings-nav">
        <a href="#general" class="settings-nav-item active" onclick="showSection('general')">
            <i class="fas fa-cog"></i> General
        </a>
        <a href="#database" class="settings-nav-item" onclick="showSection('database')">
            <i class="fas fa-database"></i> Database
        </a>
        <a href="#tmdb" class="settings-nav-item" onclick="showSection('tmdb')">
            <i class="fas fa-film"></i> TMDB API
        </a>
        <a href="#content" class="settings-nav-item" onclick="showSection('content')">
            <i class="fas fa-video"></i> Content
        </a>
        <a href="#performance" class="settings-nav-item" onclick="showSection('performance')">
            <i class="fas fa-tachometer-alt"></i> Performance
        </a>
        <a href="#security" class="settings-nav-item" onclick="showSection('security')">
            <i class="fas fa-shield-alt"></i> Security
        </a>
        <a href="#backup" class="settings-nav-item" onclick="showSection('backup')">
            <i class="fas fa-save"></i> Backup
        </a>
    </div>

    <!-- Settings Content -->
    <div class="settings-content">
        <!-- General Settings -->
        <div id="general" class="settings-section active">
            <h2 class="section-title">General Settings</h2>
            
            <div class="config-grid">
                <div class="config-card">
                    <div class="config-header">
                        <div class="config-icon" style="background: var(--primary-color);">
                            <i class="fas fa-palette"></i>
                        </div>
                        <div class="config-title">Appearance</div>
                    </div>
                    <div class="config-description">Customize the look and feel of the application</div>
                    
                    <div class="form-group">
                        <label class="form-label">Theme</label>
                        <select class="form-input">
                            <option value="dark">Dark Theme</option>
                            <option value="light">Light Theme</option>
                            <option value="auto">Auto (System)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Language</label>
                        <select class="form-input">
                            <option value="en">English</option>
                            <option value="es">Español</option>
                            <option value="fr">Français</option>
                        </select>
                    </div>
                </div>
                
                <div class="config-card">
                    <div class="config-header">
                        <div class="config-icon" style="background: var(--success-color);">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="config-title">Notifications</div>
                    </div>
                    <div class="config-description">Configure notification preferences</div>
                    
                    <div class="form-group">
                        <label class="form-label">
                            <span>Enable Notifications</span>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">
                            <span>Auto-dismiss Notifications</span>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Settings -->
        <div id="database" class="settings-section">
            <h2 class="section-title">Database Configuration</h2>
            
            <div class="status-indicator success">
                <i class="fas fa-check-circle"></i>
                Database connection is healthy
            </div>
            
            <div class="form-group">
                <label class="form-label">Database Host</label>
                <input type="text" class="form-input" value="localhost" placeholder="Database host">
                <div class="form-description">The hostname or IP address of your MySQL server</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Database Port</label>
                <input type="number" class="form-input" value="3306" placeholder="3306">
                <div class="form-description">MySQL server port (default: 3306)</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Database Name</label>
                <input type="text" class="form-input" value="iptv_xui" placeholder="Database name">
                <div class="form-description">Name of your XUI One database</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Username</label>
                <input type="text" class="form-input" value="root" placeholder="Database username">
            </div>
            
            <div class="form-group">
                <label class="form-label">Password</label>
                <input type="password" class="form-input" placeholder="Database password">
            </div>
        </div>

        <!-- TMDB Settings -->
        <div id="tmdb" class="settings-section">
            <h2 class="section-title">TMDB API Configuration</h2>
            
            <div class="form-group">
                <label class="form-label">API Key</label>
                <input type="text" class="form-input" value="201066b4b17391d478e55247f43eed64" placeholder="TMDB API Key">
                <div class="form-description">Your TMDB API key for metadata enrichment</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Language</label>
                <select class="form-input">
                    <option value="es-ES">Spanish (Spain)</option>
                    <option value="en-US">English (US)</option>
                    <option value="fr-FR">French (France)</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">
                    <span>Enable Caching</span>
                    <label class="toggle-switch">
                        <input type="checkbox" checked>
                        <span class="toggle-slider"></span>
                    </label>
                </label>
                <div class="form-description">Cache TMDB responses to reduce API calls</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Cache Duration (hours)</label>
                <input type="number" class="form-input" value="24" min="1" max="168">
                <div class="form-description">How long to cache TMDB data (1-168 hours)</div>
            </div>
        </div>

        <!-- Content Settings -->
        <div id="content" class="settings-section">
            <h2 class="section-title">Content Management</h2>
            
            <div class="config-grid">
                <div class="config-card">
                    <div class="config-header">
                        <div class="config-icon" style="background: var(--warning-color);">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="config-title">Priority Rules</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">
                            <span>Protect 4K Content</span>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">
                            <span>Protect 60fps Content</span>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">
                            <span>Protect Symlink Content</span>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </label>
                    </div>
                </div>
                
                <div class="config-card">
                    <div class="config-header">
                        <div class="config-icon" style="background: var(--error-color);">
                            <i class="fas fa-copy"></i>
                        </div>
                        <div class="config-title">Duplicate Handling</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Similarity Threshold</label>
                        <input type="range" class="form-input" min="0.5" max="1" step="0.05" value="0.85">
                        <div class="form-description">Minimum similarity to consider content as duplicate (85%)</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">
                            <span>Auto-detect Duplicates</span>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Settings -->
        <div id="performance" class="settings-section">
            <h2 class="section-title">Performance Optimization</h2>
            
            <div class="form-group">
                <label class="form-label">Page Size</label>
                <select class="form-input">
                    <option value="25">25 items</option>
                    <option value="50" selected>50 items</option>
                    <option value="100">100 items</option>
                    <option value="200">200 items</option>
                </select>
                <div class="form-description">Number of items to display per page</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Batch Size</label>
                <input type="number" class="form-input" value="100" min="10" max="1000">
                <div class="form-description">Number of items to process in each batch</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">
                    <span>Enable Caching</span>
                    <label class="toggle-switch">
                        <input type="checkbox" checked>
                        <span class="toggle-slider"></span>
                    </label>
                </label>
                <div class="form-description">Cache database queries for better performance</div>
            </div>
        </div>

        <!-- Security Settings -->
        <div id="security" class="settings-section">
            <h2 class="section-title">Security Configuration</h2>
            
            <div class="form-group">
                <label class="form-label">Session Timeout (minutes)</label>
                <input type="number" class="form-input" value="60" min="5" max="480">
                <div class="form-description">Automatic logout after inactivity</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Max Upload Size (MB)</label>
                <input type="number" class="form-input" value="50" min="1" max="500">
                <div class="form-description">Maximum file size for M3U uploads</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">
                    <span>Enable CSRF Protection</span>
                    <label class="toggle-switch">
                        <input type="checkbox" checked>
                        <span class="toggle-slider"></span>
                    </label>
                </label>
            </div>
        </div>

        <!-- Backup Settings -->
        <div id="backup" class="settings-section">
            <h2 class="section-title">Backup & Restore</h2>
            
            <div class="form-group">
                <label class="form-label">
                    <span>Auto Backup</span>
                    <label class="toggle-switch">
                        <input type="checkbox">
                        <span class="toggle-slider"></span>
                    </label>
                </label>
                <div class="form-description">Automatically backup configuration daily</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Backup Location</label>
                <input type="text" class="form-input" value="/var/backups/iptv-manager" placeholder="Backup directory">
            </div>
            
            <div class="settings-actions">
                <button class="btn btn-primary" onclick="createBackup()">
                    <i class="fas fa-save"></i>
                    Create Backup
                </button>
                <button class="btn btn-secondary" onclick="restoreBackup()">
                    <i class="fas fa-upload"></i>
                    Restore Backup
                </button>
            </div>
        </div>

        <!-- Save Actions -->
        <div class="settings-actions">
            <button class="btn btn-primary" onclick="saveSettings()">
                <i class="fas fa-save"></i>
                Save Settings
            </button>
            <button class="btn btn-secondary" onclick="resetSettings()">
                <i class="fas fa-undo"></i>
                Reset to Defaults
            </button>
            <button class="btn btn-secondary" onclick="testConnection()">
                <i class="fas fa-plug"></i>
                Test Connection
            </button>
        </div>
    </div>
</div>

<script>
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.settings-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Remove active class from nav items
    document.querySelectorAll('.settings-nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // Show selected section
    document.getElementById(sectionId).classList.add('active');
    
    // Add active class to clicked nav item
    event.target.classList.add('active');
}

function saveSettings() {
    showNotification('Settings saved successfully!', 'success');
    // TODO: Implement settings save
}

function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to defaults?')) {
        showNotification('Settings reset to defaults', 'info');
        // TODO: Implement settings reset
    }
}

function testConnection() {
    showNotification('Testing database connection...', 'info');
    // TODO: Implement connection test
    setTimeout(() => {
        showNotification('Database connection successful!', 'success');
    }, 2000);
}

function createBackup() {
    showNotification('Creating backup...', 'info');
    // TODO: Implement backup creation
}

function restoreBackup() {
    showNotification('Select backup file to restore...', 'info');
    // TODO: Implement backup restore
}
</script>
