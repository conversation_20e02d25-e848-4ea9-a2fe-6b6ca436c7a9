"""
Dashboard principal
==================

Panel principal con resumen de estadísticas, gráficos y acciones rápidas.
"""

import tkinter as tk
import customtkinter as ctk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd
import numpy as np
import asyncio
import logging
from typing import Dict, List, Optional, Any

class Dashboard:
    """Panel principal con estadísticas y gráficos"""
    
    def __init__(self, parent, settings, db_manager):
        self.parent = parent
        self.settings = settings
        self.db_manager = db_manager
        self.logger = logging.getLogger("iptv_manager.dashboard")
        
        # Configurar matplotlib para tema oscuro
        plt.style.use('dark_background' if settings.theme == 'dark' else 'default')
        
        # Crear componentes
        self._create_layout()
        self._create_stats_cards()
        self._create_charts()
        self._create_quick_actions()
        
        # Cargar datos iniciales de forma más segura
        self._schedule_data_load()
    
    def _create_layout(self):
        """Crear layout principal"""
        # Frame principal
        self.main_frame = ctk.CTkScrollableFrame(self.parent)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Título
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Panel Principal",
            font=("Arial", 24, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # Frame de estadísticas
        self.stats_frame = ctk.CTkFrame(self.main_frame)
        self.stats_frame.pack(fill="x", pady=(0, 20))
        
        # Frame de gráficos
        self.charts_frame = ctk.CTkFrame(self.main_frame)
        self.charts_frame.pack(fill="both", expand=True, pady=(0, 20))
        
        # Frame de acciones rápidas
        self.actions_frame = ctk.CTkFrame(self.main_frame)
        self.actions_frame.pack(fill="x", pady=(0, 20))
    
    def _create_stats_cards(self):
        """Crear tarjetas de estadísticas"""
        # Configurar grid
        self.stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)
        
        # Tarjetas de estadísticas
        self.stats_cards = {}
        
        stats_config = [
            ("movies", "🎬", "Películas", "0", self.settings.colors["info"]),
            ("series", "📺", "Series", "0", self.settings.colors["success"]),
            ("m3u_entries", "�", "TV en Vivo", "0", self.settings.colors["warning"]),
            ("duplicates", "❌", "Sin TMDB", "0", self.settings.colors["error"])
        ]
        
        for i, (key, icon, title, value, color) in enumerate(stats_config):
            card = self._create_stat_card(self.stats_frame, icon, title, value, color)
            card.grid(row=0, column=i, padx=10, pady=10, sticky="ew")
            self.stats_cards[key] = card
    
    def _create_stat_card(self, parent, icon: str, title: str, value: str, color: str):
        """Crear tarjeta de estadística"""
        # Frame de la tarjeta
        card_frame = ctk.CTkFrame(parent, corner_radius=15)
        
        # Contenido
        content_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Icono
        icon_label = ctk.CTkLabel(
            content_frame,
            text=icon,
            font=("Arial", 32),
            text_color=color
        )
        icon_label.pack(pady=(0, 10))
        
        # Valor
        value_label = ctk.CTkLabel(
            content_frame,
            text=value,
            font=("Arial", 24, "bold"),
            text_color=color
        )
        value_label.pack()
        
        # Título
        title_label = ctk.CTkLabel(
            content_frame,
            text=title,
            font=("Arial", 12),
            text_color=self.settings.colors["text_secondary"]
        )
        title_label.pack(pady=(5, 0))
        
        # Guardar referencia al label del valor
        card_frame.value_label = value_label
        
        return card_frame
    
    def _create_charts(self):
        """Crear gráficos"""
        # Configurar layout
        self.charts_frame.grid_columnconfigure((0, 1), weight=1)
        self.charts_frame.grid_rowconfigure((0, 1), weight=1)
        
        # Gráfico de popularidad
        self.popularity_chart = self._create_chart_frame(
            self.charts_frame,
            "Popularidad por Tipo",
            0, 0
        )
        
        # Gráfico de tendencias
        self.trends_chart = self._create_chart_frame(
            self.charts_frame,
            "Estado TMDB",
            0, 1
        )
        
        # Gráfico de calidad
        self.quality_chart = self._create_chart_frame(
            self.charts_frame,
            "Distribución de Calidad",
            1, 0
        )
        
        # Gráfico de idiomas
        self.language_chart = self._create_chart_frame(
            self.charts_frame,
            "Distribución de Idiomas",
            1, 1
        )
    
    def _create_chart_frame(self, parent, title: str, row: int, col: int):
        """Crear frame para gráfico"""
        # Frame del gráfico
        chart_frame = ctk.CTkFrame(parent, corner_radius=15)
        chart_frame.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")
        
        # Título
        title_label = ctk.CTkLabel(
            chart_frame,
            text=title,
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(15, 10))
        
        # Frame para el gráfico
        graph_frame = ctk.CTkFrame(chart_frame, fg_color="transparent")
        graph_frame.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        return graph_frame
    
    def _create_quick_actions(self):
        """Crear acciones rápidas"""
        # Título
        title_label = ctk.CTkLabel(
            self.actions_frame,
            text="Acciones Rápidas",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(15, 10))
        
        # Frame de botones
        buttons_frame = ctk.CTkFrame(self.actions_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # Configurar grid
        buttons_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)
        
        # Botones de acción
        actions = [
            ("📁", "Importar M3U", self._import_m3u),
            ("🔍", "Buscar TMDB", self._search_tmdb),
            ("🔄", "Encontrar Duplicados", self._find_duplicates),
            ("📊", "Generar Reporte", self._generate_report)
        ]
        
        for i, (icon, text, command) in enumerate(actions):
            button = ctk.CTkButton(
                buttons_frame,
                text=f"{icon}\\n{text}",
                font=("Arial", 12),
                height=80,
                corner_radius=15,
                command=command
            )
            button.grid(row=0, column=i, padx=10, pady=10, sticky="ew")
    
    async def _load_data(self):
        """Cargar datos para el dashboard"""
        try:
            # Obtener estadísticas
            stats = await self.db_manager.get_content_stats()
            
            # Actualizar tarjetas
            if stats:
                self._update_stats_cards(stats)
            
            # Generar gráficos
            await self._generate_charts()
            
        except Exception as e:
            self.logger.error(f"Error al cargar datos del dashboard: {str(e)}")
    
    def _update_stats_cards(self, stats: Dict[str, Any]):
        """Actualizar tarjetas de estadísticas"""
        updates = {
            "movies": str(stats.get("total_movies", 0)),
            "series": str(stats.get("total_series", 0)),
            "m3u_entries": str(stats.get("total_live", 0)),  # Usar live streams para M3U
            "duplicates": str(stats.get("without_tmdb", 0))  # Usar sin TMDB como proxy de duplicados
        }
        
        for key, value in updates.items():
            if key in self.stats_cards:
                self.stats_cards[key].value_label.configure(text=value)
    
    async def _generate_charts(self):
        """Generar gráficos"""
        try:
            # Gráfico de popularidad
            await self._create_popularity_chart()
            
            # Gráfico de tendencias
            await self._create_trends_chart()
            
            # Gráfico de calidad
            await self._create_quality_chart()
            
            # Gráfico de idiomas
            await self._create_language_chart()
            
        except Exception as e:
            self.logger.error(f"Error al generar gráficos: {str(e)}")
    
    async def _create_popularity_chart(self):
        """Crear gráfico de popularidad"""
        try:
            # Obtener datos reales
            stats = await self.db_manager.get_content_stats()
            
            # Datos reales
            labels = ['Películas', 'TV en Vivo', 'Otros Streams']
            sizes = [
                stats.get("total_movies", 0),
                stats.get("total_live", 0),
                stats.get("total_streams", 0) - stats.get("total_movies", 0) - stats.get("total_live", 0)
            ]
            colors = ['#ff6b6b', '#4ecdc4', '#45b7d1']
            
            # Filtrar valores cero
            filtered_data = [(label, size, color) for label, size, color in zip(labels, sizes, colors) if size > 0]
            if filtered_data:
                labels, sizes, colors = zip(*filtered_data)
            
            # Crear figura
            fig, ax = plt.subplots(figsize=(5, 4))
            ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax.set_title('Distribución de Contenido', fontsize=14, fontweight='bold')
            
        except Exception as e:
            self.logger.error(f"Error al crear gráfico de popularidad: {str(e)}")
            # Fallback a datos de ejemplo
            labels = ['Películas', 'TV en Vivo', 'Otros']
            sizes = [45, 25, 30]
            colors = ['#ff6b6b', '#4ecdc4', '#45b7d1']
            
            fig, ax = plt.subplots(figsize=(5, 4))
            ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax.set_title('Distribución de Contenido', fontsize=14, fontweight='bold')
        
        # Agregar a la interfaz
        canvas = FigureCanvasTkAgg(fig, self.popularity_chart)
        canvas.draw()
        canvas.get_tk_widget().pack(fill="both", expand=True)
    
    async def _create_trends_chart(self):
        """Crear gráfico de tendencias - TMDB vs Sin TMDB"""
        try:
            # Obtener datos reales
            stats = await self.db_manager.get_content_stats()
            
            # Datos de TMDB
            categories = ['Con TMDB', 'Sin TMDB']
            values = [
                stats.get("with_tmdb", 0),
                stats.get("without_tmdb", 0)
            ]
            colors = ['#4ecdc4', '#ff6b6b']
            
            # Crear figura
            fig, ax = plt.subplots(figsize=(5, 4))
            bars = ax.bar(categories, values, color=colors)
            ax.set_title('Contenido con/sin TMDB', fontsize=14, fontweight='bold')
            ax.set_ylabel('Cantidad')
            
            # Agregar valores en las barras
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{int(height)}', ha='center', va='bottom')
            
        except Exception as e:
            self.logger.error(f"Error al crear gráfico de tendencias: {str(e)}")
            # Fallback a datos de ejemplo
            categories = ['Con TMDB', 'Sin TMDB']
            values = [15000, 5000]
            colors = ['#4ecdc4', '#ff6b6b']
            
            fig, ax = plt.subplots(figsize=(5, 4))
            bars = ax.bar(categories, values, color=colors)
            ax.set_title('Contenido con/sin TMDB', fontsize=14, fontweight='bold')
            ax.set_ylabel('Cantidad')
            
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{int(height)}', ha='center', va='bottom')
        
        # Agregar a la interfaz
        canvas = FigureCanvasTkAgg(fig, self.trends_chart)
        canvas.draw()
        canvas.get_tk_widget().pack(fill="both", expand=True)
    
    async def _create_quality_chart(self):
        """Crear gráfico de calidad"""
        # Datos de ejemplo
        qualities = ['4K', '1080p', '720p', '480p', 'SD']
        counts = [5, 25, 35, 20, 15]
        
        # Crear figura
        fig, ax = plt.subplots(figsize=(5, 4))
        bars = ax.bar(qualities, counts, color=['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'])
        ax.set_title('Distribución de Calidad', fontsize=14, fontweight='bold')
        ax.set_ylabel('Cantidad')
        
        # Agregar valores en las barras
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{int(height)}', ha='center', va='bottom')
        
        # Agregar a la interfaz
        canvas = FigureCanvasTkAgg(fig, self.quality_chart)
        canvas.draw()
        canvas.get_tk_widget().pack(fill="both", expand=True)
    
    async def _create_language_chart(self):
        """Crear gráfico de idiomas"""
        # Datos de ejemplo
        languages = ['Español', 'Inglés', 'Francés', 'Alemán', 'Otros']
        counts = [40, 30, 15, 10, 5]
        colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']
        
        # Crear figura
        fig, ax = plt.subplots(figsize=(5, 4))
        bars = ax.barh(languages, counts, color=colors)
        ax.set_title('Distribución de Idiomas', fontsize=14, fontweight='bold')
        ax.set_xlabel('Cantidad')
        
        # Agregar valores en las barras
        for bar in bars:
            width = bar.get_width()
            ax.text(width, bar.get_y() + bar.get_height()/2.,
                   f'{int(width)}', ha='left', va='center')
        
        # Agregar a la interfaz
        canvas = FigureCanvasTkAgg(fig, self.language_chart)
        canvas.draw()
        canvas.get_tk_widget().pack(fill="both", expand=True)
    
    def _import_m3u(self):
        """Importar archivo M3U"""
        # Implementar importación M3U
        pass
    
    def _search_tmdb(self):
        """Buscar en TMDB"""
        # Implementar búsqueda TMDB
        pass
    
    def _find_duplicates(self):
        """Encontrar duplicados"""
        # Implementar búsqueda de duplicados
        pass
    
    def _generate_report(self):
        """Generar reporte"""
        # Implementar generación de reporte
        pass
    
    def _schedule_data_load(self):
        """Programar carga de datos de forma segura"""
        # Cargar datos después de que la UI esté lista
        self.parent.after(100, self._load_data_sync)
    
    def _load_data_sync(self):
        """Cargar datos de forma síncrona usando asyncio"""
        try:
            # Crear tarea asíncrona
            asyncio.create_task(self._load_data())
        except Exception as e:
            self.logger.error(f"Error al programar carga de datos: {str(e)}")
            # Fallback: cargar datos básicos
            self._load_fallback_data()
    
    def _load_fallback_data(self):
        """Cargar datos básicos de fallback"""
        # Actualizar con datos por defecto
        fallback_stats = {
            "total_movies": 0,
            "total_series": 0,
            "total_live": 0,
            "without_tmdb": 0
        }
        self._update_stats_cards(fallback_stats)
    
    def destroy(self):
        """Destruir componente"""
        if hasattr(self, 'main_frame'):
            self.main_frame.destroy()
