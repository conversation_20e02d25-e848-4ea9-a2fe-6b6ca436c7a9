<?php
/**
 * Database Configuration for IPTV XUI One Content Manager
 * ======================================================
 *
 * Configuration for connecting to XUI One MySQL database
 * with optimized settings for performance and reliability.
 */

// Load environment variables
require_once __DIR__ . '/env.php';

// Load environment if not already loaded
if (!isset($_ENV['DB_HOST'])) {
    loadEnvironmentVariables();
}

/**
 * Get database configuration
 */
function getDatabaseConfig() {
    return [
        'host' => env('DB_HOST', 'localhost'),
        'port' => (int)env('DB_PORT', 3306),
        'database' => env('DB_NAME', 'iptv_xui'),
        'username' => env('DB_USER', 'root'),
        'password' => env('DB_PASSWORD', ''),
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci'
    ];
}

// Database connection configuration (using function to avoid PDO constant issues)
define('DB_CONFIG', getDatabaseConfig());

// Connection pool settings (optimized to prevent long threads)
define('CONNECTION_POOL', [
    'max_connections' => 5,
    'timeout' => 10,
    'retry_attempts' => 2,
    'retry_delay' => 1,
    'wait_timeout' => 30,
    'interactive_timeout' => 30,
    'net_read_timeout' => 10,
    'net_write_timeout' => 10
]);

// SQL Queries for XUI One database operations
define('SQL_QUERIES', [
    
    // ===== CONTENT ANALYSIS QUERIES =====
    
    'get_content_stats' => "
        SELECT 
            COUNT(*) as total_streams,
            SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END) as live_tv,
            SUM(CASE WHEN type = 2 THEN 1 ELSE 0 END) as movies,
            SUM(CASE WHEN type = 3 THEN 1 ELSE 0 END) as series,
            SUM(CASE WHEN type = 2 AND movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_movies,
            SUM(CASE WHEN type = 2 AND direct_source = 1 THEN 1 ELSE 0 END) as direct_movies
        FROM streams
    ",
    
    'get_quality_stats' => "
        SELECT 
            SUM(CASE WHEN stream_display_name LIKE '%4K%' OR stream_display_name LIKE '%2160p%' THEN 1 ELSE 0 END) as content_4k,
            SUM(CASE WHEN stream_display_name LIKE '%60fps%' OR stream_display_name LIKE '%60FPS%' THEN 1 ELSE 0 END) as content_60fps,
            SUM(CASE WHEN stream_display_name LIKE '%HDR%' THEN 1 ELSE 0 END) as content_hdr,
            SUM(CASE WHEN stream_display_name LIKE '%1080p%' OR stream_display_name LIKE '%FHD%' THEN 1 ELSE 0 END) as content_fhd,
            SUM(CASE WHEN stream_display_name LIKE '%720p%' OR stream_display_name LIKE '%HD%' THEN 1 ELSE 0 END) as content_hd
        FROM streams 
        WHERE type = 2
    ",
    
    'get_duplicate_analysis' => "
        SELECT 
            stream_display_name,
            COUNT(*) as duplicate_count,
            GROUP_CONCAT(id) as stream_ids,
            SUM(CASE WHEN movie_symlink = 1 THEN 1 ELSE 0 END) as symlink_count,
            SUM(CASE WHEN direct_source = 1 THEN 1 ELSE 0 END) as direct_count
        FROM streams 
        WHERE type = 2 
        GROUP BY stream_display_name 
        HAVING COUNT(*) > 1
        ORDER BY duplicate_count DESC
        LIMIT ? OFFSET ?
    ",
    
    // ===== CONTENT RETRIEVAL QUERIES =====
    
    'get_movies_paginated' => "
        SELECT s.*, sc.category_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        WHERE s.type = 2 
        ORDER BY s.added DESC 
        LIMIT ? OFFSET ?
    ",
    
    'get_symlink_content' => "
        SELECT s.*, sc.category_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        WHERE s.type = 2 AND s.movie_symlink = 1
        ORDER BY s.added DESC 
        LIMIT ? OFFSET ?
    ",
    
    'get_direct_source_content' => "
        SELECT s.*, sc.category_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        WHERE s.type = 2 AND s.direct_source = 1
        ORDER BY s.added DESC 
        LIMIT ? OFFSET ?
    ",
    
    'get_4k_content' => "
        SELECT s.*, sc.category_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        WHERE s.type = 2 AND s.movie_symlink = 1 
        AND (s.stream_display_name LIKE '%4K%' OR s.stream_display_name LIKE '%2160p%')
        ORDER BY s.added DESC 
        LIMIT ? OFFSET ?
    ",
    
    'get_60fps_content' => "
        SELECT s.*, sc.category_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        WHERE s.type = 2 AND s.movie_symlink = 1 
        AND (s.stream_display_name LIKE '%60fps%' OR s.stream_display_name LIKE '%60FPS%')
        ORDER BY s.added DESC 
        LIMIT ? OFFSET ?
    ",
    
    // ===== SEARCH QUERIES =====
    
    'search_content' => "
        SELECT s.*, sc.category_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        WHERE s.stream_display_name LIKE ? 
        OR s.movie_properties LIKE ?
        ORDER BY s.added DESC 
        LIMIT ? OFFSET ?
    ",
    
    'search_missing_tmdb' => "
        SELECT s.*, sc.category_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        WHERE s.type = 2 
        AND (s.tmdb_id IS NULL OR s.tmdb_id = 0)
        ORDER BY s.added DESC 
        LIMIT ? OFFSET ?
    ",
    
    // ===== CONTENT MANAGEMENT QUERIES =====
    
    'insert_stream' => "
        INSERT INTO streams (
            type, category_id, stream_display_name, stream_source, stream_icon,
            tmdb_id, year, rating, movie_properties, added, updated,
            movie_symlink, direct_source
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ",
    
    'update_stream_tmdb' => "
        UPDATE streams 
        SET tmdb_id = ?, movie_properties = ?, year = ?, rating = ?, updated = ?
        WHERE id = ?
    ",
    
    'delete_stream' => "
        DELETE FROM streams WHERE id = ?
    ",
    
    // ===== CATEGORY QUERIES =====
    
    'get_categories' => "
        SELECT * FROM streams_categories 
        ORDER BY cat_order ASC, category_name ASC
    ",
    
    'get_movie_categories' => "
        SELECT * FROM streams_categories 
        WHERE category_type = 'movie' 
        ORDER BY cat_order ASC, category_name ASC
    ",
    
    // ===== SERIES QUERIES =====
    
    'get_series' => "
        SELECT ss.*, sc.category_name 
        FROM streams_series ss
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, ss.category_id)
        ORDER BY ss.last_modified DESC 
        LIMIT ? OFFSET ?
    ",
    
    'get_series_episodes' => "
        SELECT se.* 
        FROM streams_episodes se
        WHERE se.series_id = ?
        ORDER BY se.season_num ASC, se.episode_num ASC
    ",
    
    // ===== ANALYTICS QUERIES =====
    
    'get_popular_content' => "
        SELECT s.*, sc.category_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        WHERE s.type = 2 AND s.rating > 0
        ORDER BY s.rating DESC 
        LIMIT ?
    ",
    
    'get_recent_additions' => "
        SELECT s.*, sc.category_name 
        FROM streams s 
        LEFT JOIN streams_categories sc ON FIND_IN_SET(sc.id, s.category_id)
        WHERE s.type = 2 
        ORDER BY s.added DESC 
        LIMIT ?
    ",
    
    'get_content_by_year' => "
        SELECT 
            s.year,
            COUNT(*) as content_count,
            AVG(s.rating) as avg_rating
        FROM streams s 
        WHERE s.type = 2 AND s.year IS NOT NULL AND s.year > 0
        GROUP BY s.year 
        ORDER BY s.year DESC
        LIMIT ?
    "
]);

// Table validation queries
define('TABLE_VALIDATION', [
    'check_streams_table' => "SHOW TABLES LIKE 'streams'",
    'check_streams_categories_table' => "SHOW TABLES LIKE 'streams_categories'",
    'check_streams_series_table' => "SHOW TABLES LIKE 'streams_series'",
    'check_streams_episodes_table' => "SHOW TABLES LIKE 'streams_episodes'",
    
    'validate_streams_structure' => "
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'streams'
    "
]);

// Cache configuration
define('CACHE_CONFIG', [
    'enabled' => true,
    'default_ttl' => 3600, // 1 hour
    'tmdb_ttl' => 86400,   // 24 hours
    'stats_ttl' => 300     // 5 minutes
]);

// Batch processing limits
define('BATCH_LIMITS', [
    'default_page_size' => 50,
    'max_page_size' => 500,
    'batch_insert_size' => 100,
    'max_execution_time' => 30
]);
